globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,r as renderComponent,d as renderTemplate,m as maybeRenderHead}from"../../../chunks/astro/server_BdgiS2eL.mjs";import{$ as $$Layout}from"../../../chunks/Layout_DzMAt9ck.mjs";import{$ as $$ProductCard}from"../../../chunks/ProductCard_mNAa5Up9.mjs";import{a as $$StructuredData}from"../../../chunks/StructuredData_CYM00pJx.mjs";import{c as createPolarClient,t as transformPolarProduct,d as getProductsByTag,g as getTagDisplayName}from"../../../chunks/polar_D7XkB6p_.mjs";export{renderers}from"../../../renderers.mjs";const $$Astro=createAstro("https://infpik.store"),$$tag=createComponent((async(e,t,r)=>{const a=e.createAstro($$Astro,t,r);a.self=$$tag;const{tag:o}=a.params;if(!o)return a.redirect("/products");let s=[],n=[],l=null;try{const e=a.locals?.runtime?.env,t=createPolarClient(e),r=e?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(r){const e=await t.products.list({organizationId:r,isArchived:!1});s=(e.result?.items||[]).map(transformPolarProduct).filter((e=>null!==e)),n=getProductsByTag(s,o)}else;}catch(e){console.error("Error fetching products for tag:",e),l="Failed to load products"}const c=getTagDisplayName(o),i={items:[{name:"Home",url:"http://infpik.store"},{name:"Products",url:"http://infpik.store/products"},{name:c,url:`http://infpik.store/products/tag/${o}`}]};return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:`${c} - InfPik`,description:`Browse our collection of ${c.toLowerCase()} digital images and artwork. High-quality digital assets tagged with ${c.toLowerCase()}.`,canonical:`http://infpik.store/products/tag/${o}`},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:i})} ${maybeRenderHead()}<div class="w-full px-4 md:px-8 py-8"> <section class="text-center mb-12"> <h1 class="text-4xl font-bold text-gray-900 mb-4">${c}</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto"> ${n.length>0?`Discover ${n.length} ${1===n.length?"item":"items"} images with ${c.toLowerCase()}`:`No images found with ${c.toLowerCase()}`} </p> </section> ${0===n.length?renderTemplate`<div class="text-center py-16"> <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"> <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path> </svg> </div> <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Items with ${c}</h3> <p class="text-gray-600 mb-8">We don't have any images with ${c.toLowerCase()} yet.</p> <a href="/products" class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg>
Browse All Products
</a> </div>`:renderTemplate`<div class="columns-2 sm:columns-3 lg:columns-4 gap-6"> ${n.slice(0,16).map((t=>renderTemplate`${renderComponent(e,"ProductCard",$$ProductCard,{product:t})}`))} </div>`} </div> `})}`}),"D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro",$$url="/products/tag/[tag]",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$tag,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};