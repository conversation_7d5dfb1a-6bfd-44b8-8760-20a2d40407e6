globalThis.process??={},globalThis.process.env??={};import{a as createComponent,r as renderComponent,d as renderTemplate,m as maybeRenderHead}from"../chunks/astro/server_BdgiS2eL.mjs";import{$ as $$Layout}from"../chunks/Layout_DzMAt9ck.mjs";import{$ as $$ProductCard}from"../chunks/ProductCard_mNAa5Up9.mjs";import{a as $$StructuredData}from"../chunks/StructuredData_CYM00pJx.mjs";import{f as getTrendingProducts}from"../chunks/polar_D7XkB6p_.mjs";export{renderers}from"../renderers.mjs";const $$Trending=createComponent((async(e,r,t)=>{let a=[],o=null;try{a=await getTrendingProducts(7,12)}catch(e){console.error("Error fetching trending products:",e),o="Failed to load trending products"}const n={items:[{name:"Home",url:"http://infpik.store"},{name:"Trending",url:"http://infpik.store/trending"}]};return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:"Trending Products - InfPik",description:"Discover the most popular digital images and artwork based on recent sales. Updated daily to showcase what's trending right now.",canonical:"http://infpik.store/trending"},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:n})} ${maybeRenderHead()}<div class="w-full px-4 md:px-8 py-8"> <section class="text-center mb-12"> <h1 class="text-4xl font-bold text-gray-900 mb-4">Trending Now</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto">
Our top-selling images from the past week, loved by creators worldwide
</p> </section> ${o&&renderTemplate`<div class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8"> <div class="flex items-center gap-3 text-red-800"> <svg class="w-6 h-6 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path> </svg> <div> <p class="font-semibold">⚠️ ${o}</p> <p class="text-sm">Please try again later.</p> </div> </div> </div>`} ${!o&&0===a.length&&renderTemplate`<div class="text-center py-16"> <p class="text-lg text-gray-600">No trending products available right now. Check back soon!</p> </div>`} ${!o&&a.length>0&&renderTemplate`<div class="columns-2 sm:columns-3 lg:columns-4 gap-6"> ${a.slice(0,16).map((r=>renderTemplate`${renderComponent(e,"ProductCard",$$ProductCard,{product:r})}`))} </div>`} </div> `})}`}),"D:/code/image/polar-image-store/src/pages/trending.astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/trending.astro",$$url="/trending",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$Trending,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};