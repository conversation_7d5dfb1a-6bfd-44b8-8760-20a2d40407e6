globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,m as maybeRenderHead,r as renderComponent,f as renderScript,b as addAttribute,d as renderTemplate}from"../../chunks/astro/server_BdgiS2eL.mjs";import{$ as $$Layout}from"../../chunks/Layout_DzMAt9ck.mjs";import{$ as $$OptimizedImage,a as $$StructuredData}from"../../chunks/StructuredData_CYM00pJx.mjs";import{c as createPolarClient,t as transformPolarProduct,j as formatPrice}from"../../chunks/polar_D7XkB6p_.mjs";export{renderers}from"../../renderers.mjs";const $$Astro$5=createAstro("https://infpik.store"),$$ImageGallery=createComponent(((e,t,a)=>{const r=e.createAstro($$Astro$5,t,a);r.self=$$ImageGallery;const{images:s,productName:o}=r.props,i=s.length>0?s:["/placeholder-image.svg"];return renderTemplate`${maybeRenderHead()}<div class="flex flex-col gap-4" data-astro-cid-gjhjmbi3> <div class="relative aspect-video rounded-2xl overflow-hidden bg-gray-50 cursor-zoom-in flex items-center justify-center group" data-astro-cid-gjhjmbi3> ${renderComponent(e,"OptimizedImage",$$OptimizedImage,{id:"mainImage",src:i[0],alt:o,preset:"productDetail",loading:"eager",fetchpriority:"high",onclick:"openLightbox(0)",class:"max-w-full max-h-full w-auto h-auto object-contain transition-transform duration-300 group-hover:scale-105","data-astro-cid-gjhjmbi3":!0})} </div> ${i.length>1&&renderTemplate`<div class="grid grid-cols-[repeat(auto-fit,minmax(80px,1fr))] gap-2 max-h-24 overflow-x-auto" data-astro-cid-gjhjmbi3> ${i.map(((t,a)=>renderTemplate`<div${addAttribute("aspect-square rounded-full overflow-hidden cursor-pointer border-2 transition-all duration-200 hover:border-primary-500 hover:scale-105 "+(0===a?"border-primary-500":"border-transparent"),"class")} data-astro-cid-gjhjmbi3> ${renderComponent(e,"OptimizedImage",$$OptimizedImage,{src:t,alt:`${o} - Image ${a+1}`,preset:"thumbnail",loading:"eager",onclick:`changeMainImage('${t}', ${a})`,class:"w-full h-full object-cover","data-astro-cid-gjhjmbi3":!0})} </div>`))} </div>`} </div> <!-- Lightbox Modal --> <div id="lightbox" class="hidden fixed inset-0 z-[1000] bg-black/90 animate-fade-in" data-astro-cid-gjhjmbi3> <div class="relative max-w-[90%] max-h-[90%] flex items-center justify-center h-full mx-auto" data-astro-cid-gjhjmbi3> <button class="absolute -top-12 right-0 text-white text-3xl font-bold bg-none border-none cursor-pointer z-[1001] hover:text-gray-300 transition-colors" onclick="closeLightbox()" data-astro-cid-gjhjmbi3>
&times;
</button> <button class="absolute top-1/2 -translate-y-1/2 -left-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80" onclick="prevImage()" data-astro-cid-gjhjmbi3>
&#8249;
</button> ${renderComponent(e,"OptimizedImage",$$OptimizedImage,{id:"lightboxImage",src:"/placeholder-image.svg",alt:"Lightbox image",width:1200,height:800,loading:"lazy",class:"max-w-full max-h-full object-contain rounded-lg","data-astro-cid-gjhjmbi3":!0})} <button class="absolute top-1/2 -translate-y-1/2 -right-20 text-white text-5xl font-bold bg-black/50 border-none cursor-pointer p-4 rounded-full transition-all duration-200 hover:bg-black/80" onclick="nextImage()" data-astro-cid-gjhjmbi3>
&#8250;
</button> <div class="absolute -bottom-12 left-1/2 -translate-x-1/2 text-white bg-black/70 px-4 py-2 rounded-full" data-astro-cid-gjhjmbi3> <span id="imageCounter" data-astro-cid-gjhjmbi3>1 / 1</span> </div> </div> </div>  ${renderScript(e,"D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts")}`}),"D:/code/image/polar-image-store/src/components/ImageGallery.astro",void 0),$$Astro$4=createAstro("https://infpik.store"),$$RelatedImages=createComponent((async(e,t,a)=>{const r=e.createAstro($$Astro$4,t,a);r.self=$$RelatedImages;const{currentProduct:s}=r.props;function o(e,t){if(!e.tags||!t.tags)return 0;const a=new Set(e.tags),r=new Set(t.tags);let s=0;for(const e of a)r.has(e)&&s++;return s}let i=[];try{const e=createPolarClient(),t="e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(t&&s.tags&&s.tags.length>0){const a=await e.products.list({organizationId:t,isArchived:!1}),r=a.result?.items||[],l=r.map(transformPolarProduct).filter((e=>null!==e)).filter((e=>e.id!==s.id&&e.tags&&e.tags.length>0));i=l.map((e=>({product:e,similarity:o(s,e)}))).filter((e=>e.similarity>0)).sort(((e,t)=>t.similarity-e.similarity)).slice(0,6).map((e=>e.product))}}catch(e){console.error("Error fetching related products:",e)}return renderTemplate`${i.length>0&&renderTemplate`${maybeRenderHead()}<section class="mt-16" data-astro-cid-4j3m3ndg><div class="mb-8" data-astro-cid-4j3m3ndg><h2 class="text-3xl font-bold text-primary-900 mb-4" data-astro-cid-4j3m3ndg>Related Images</h2><p class="text-primary-600" data-astro-cid-4j3m3ndg>Discover similar images you might like</p></div><div class="relative" data-astro-cid-4j3m3ndg><!-- Slider container --><div class="overflow-x-auto scrollbar-hide" data-astro-cid-4j3m3ndg><div class="flex gap-6 pb-4" style="width: max-content;" data-astro-cid-4j3m3ndg>${i.map((t=>renderTemplate`<div class="flex-none w-80" data-astro-cid-4j3m3ndg><div class="group bg-white rounded-2xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-xl hover:shadow-primary-500/10" data-astro-cid-4j3m3ndg>${t.images.length>0&&renderTemplate`<div class="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50" data-astro-cid-4j3m3ndg>${renderComponent(e,"OptimizedImage",$$OptimizedImage,{src:t.images[0],alt:t.name,preset:"related",loading:"lazy",class:"w-full h-full object-cover transition-all duration-300 group-hover:scale-105","data-astro-cid-4j3m3ndg":!0})}<!-- Gradient overlay --><div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" data-astro-cid-4j3m3ndg></div><!-- Hover actions --><div class="absolute inset-0 flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0" data-astro-cid-4j3m3ndg><a${addAttribute(`/products/${t.slug}`,"href")} class="flex items-center gap-2 px-4 py-2 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-medium text-sm transition-all duration-200 hover:bg-white hover:scale-105" data-astro-cid-4j3m3ndg><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-4j3m3ndg><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" data-astro-cid-4j3m3ndg></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" data-astro-cid-4j3m3ndg></path></svg>
View
</a></div><!-- Price badge --><div class="absolute top-3 right-3 px-2 py-1 bg-white/95 backdrop-blur-sm rounded-full" data-astro-cid-4j3m3ndg><span class="text-sm font-bold text-primary-900" data-astro-cid-4j3m3ndg>${formatPrice(t.price,t.currency)}</span></div></div>`}<div class="p-4" data-astro-cid-4j3m3ndg><!-- Tags -->${t.tags&&t.tags.length>0&&renderTemplate`<div class="mb-2" data-astro-cid-4j3m3ndg><div class="flex flex-wrap gap-1" data-astro-cid-4j3m3ndg>${t.tags.slice(0,2).map((e=>renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-accent-100 text-accent-700 text-xs font-medium rounded-full" data-astro-cid-4j3m3ndg>
#${e}</span>`))}${t.tags.length>2&&renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-primary-100 text-primary-600 text-xs font-medium rounded-full" data-astro-cid-4j3m3ndg>
+${t.tags.length-2}</span>`}</div></div>`}<!-- Title --><h3 class="text-lg font-bold text-primary-900 mb-2 line-clamp-2 group-hover:text-accent-600 transition-colors" data-astro-cid-4j3m3ndg>${t.name}</h3><!-- Description --><p class="text-primary-600 text-sm line-clamp-2 leading-relaxed" data-astro-cid-4j3m3ndg>${t.description}</p></div></div></div>`))}</div></div><!-- Scroll indicators (optional) --><div class="flex justify-center mt-4 gap-2" data-astro-cid-4j3m3ndg>${i.map(((e,t)=>renderTemplate`<div class="w-2 h-2 bg-primary-200 rounded-full" data-astro-cid-4j3m3ndg></div>`))}</div></div></section>`}`}),"D:/code/image/polar-image-store/src/components/RelatedImages.astro",void 0),$$Astro$3=createAstro("https://infpik.store"),$$TrustSignals=createComponent(((e,t,a)=>{const r=e.createAstro($$Astro$3,t,a);r.self=$$TrustSignals;const{class:s=""}=r.props;return renderTemplate`${maybeRenderHead()}<div${addAttribute(`flex flex-wrap items-center gap-4 text-sm text-primary-600 ${s}`,"class")}> <div class="flex items-center gap-2"> <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path> </svg> <span class="font-medium">Secure Payment</span> </div> <div class="flex items-center gap-2"> <svg class="w-4 h-4 text-blue-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> <span class="font-medium">Instant Download</span> </div> <div class="flex items-center gap-2"> <svg class="w-4 h-4 text-purple-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9"></path> </svg> <span class="font-medium">Commercial License</span> </div> </div>`}),"D:/code/image/polar-image-store/src/components/TrustSignals.astro",void 0),$$Astro$2=createAstro("https://infpik.store"),$$ProductBenefits=createComponent(((e,t,a)=>{const r=e.createAstro($$Astro$2,t,a);r.self=$$ProductBenefits;const{class:s=""}=r.props;return renderTemplate`${maybeRenderHead()}<div${addAttribute(`bg-gradient-to-br from-accent-50 to-primary-50 rounded-2xl p-6 ${s}`,"class")}> <h3 class="text-lg font-semibold text-primary-900 mb-4 flex items-center gap-2"> <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path> </svg>
Why Choose Our Digital Images?
</h3> <div class="space-y-4"> <div class="flex items-start gap-3"> <div class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <div> <h4 class="font-medium text-primary-900">Professional Quality</h4> <p class="text-sm text-primary-600">High-resolution images perfect for any project</p> </div> </div> <div class="flex items-start gap-3"> <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <div> <h4 class="font-medium text-primary-900">Commercial License</h4> <p class="text-sm text-primary-600">Use for personal and commercial projects</p> </div> </div> <div class="flex items-start gap-3"> <div class="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-purple-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <div> <h4 class="font-medium text-primary-900">Multiple Formats</h4> <p class="text-sm text-primary-600">Available in JPEG, PNG, and other formats</p> </div> </div> <div class="flex items-start gap-3"> <div class="w-6 h-6 rounded-full bg-orange-100 flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-orange-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <div> <h4 class="font-medium text-primary-900">24/7 Support</h4> <p class="text-sm text-primary-600">Get help whenever you need it</p> </div> </div> </div> </div>`}),"D:/code/image/polar-image-store/src/components/ProductBenefits.astro",void 0),$$Astro$1=createAstro("https://infpik.store"),$$ProductFAQ=createComponent(((e,t,a)=>{const r=e.createAstro($$Astro$1,t,a);r.self=$$ProductFAQ;const{class:s=""}=r.props;return renderTemplate`${maybeRenderHead()}<div${addAttribute(`bg-primary-50 rounded-2xl p-6 ${s}`,"class")} data-astro-cid-wcd6ad2o> <h3 class="text-lg font-semibold text-primary-900 mb-6 flex items-center gap-2" data-astro-cid-wcd6ad2o> <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-wcd6ad2o> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" data-astro-cid-wcd6ad2o></path> </svg>
Frequently Asked Questions
</h3> <div class="space-y-4" data-astro-cid-wcd6ad2o> ${[{id:1,question:"What formats are included with my purchase?",answer:"You'll receive high-resolution JPEG files optimized for web and print use. Additional formats like PNG may be available depending on the specific image."},{id:2,question:"Can I use these images for commercial projects?",answer:"Yes! All our images come with a commercial license that allows you to use them in both personal and commercial projects without additional fees."},{id:3,question:"How do I download my images after purchase?",answer:"After completing your purchase, you'll receive an email with download links. You can also access your downloads from your account dashboard."},{id:4,question:"Are there any usage restrictions?",answer:"Our license allows broad usage rights. You cannot resell or redistribute the images as-is, but you can use them in your creative projects and commercial work."}].map((e=>renderTemplate`<details class="group bg-white rounded-lg border border-primary-200 overflow-hidden" data-astro-cid-wcd6ad2o> <summary class="flex items-center justify-between p-4 cursor-pointer hover:bg-primary-50 transition-colors" data-astro-cid-wcd6ad2o> <h4 class="font-medium text-primary-900 pr-4" data-astro-cid-wcd6ad2o>${e.question}</h4> <svg class="w-5 h-5 text-primary-600 transform transition-transform group-open:rotate-180 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-wcd6ad2o> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" data-astro-cid-wcd6ad2o></path> </svg> </summary> <div class="px-4 pb-4" data-astro-cid-wcd6ad2o> <p class="text-primary-700 leading-relaxed" data-astro-cid-wcd6ad2o>${e.answer}</p> </div> </details>`))} </div> <div class="mt-6 pt-4 border-t border-primary-200 text-center" data-astro-cid-wcd6ad2o> <p class="text-sm text-primary-600 mb-3" data-astro-cid-wcd6ad2o>Still have questions?</p> <a href="mailto:<EMAIL>" class="inline-flex items-center gap-2 text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors" data-astro-cid-wcd6ad2o> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-wcd6ad2o> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" data-astro-cid-wcd6ad2o></path> </svg>
Contact Support
</a> </div> </div> `}),"D:/code/image/polar-image-store/src/components/ProductFAQ.astro",void 0),$$Astro=createAstro("https://infpik.store"),$$slug=createComponent((async(e,t,a)=>{const r=e.createAstro($$Astro,t,a);r.self=$$slug;const{slug:s}=r.params;if(!s)return r.redirect("/products");let o=null;try{const e=r.locals?.runtime?.env,t=createPolarClient(e),a=e?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(a){const e=await t.products.list({organizationId:a,isArchived:!1}),i=(e.result?.items||[]).map(transformPolarProduct).filter((e=>null!==e));if(o=i.find((e=>e.slug===s))||null,!o)return r.redirect("/products")}else;}catch(e){return console.error("Error fetching product:",e),r.redirect("/products")}if(!o||!o.id||!o.name)return r.redirect("/products");let i="";try{const e=r.locals?.runtime?.env,t=createPolarClient(e),a=await t.checkoutLinks.create({paymentProcessor:"stripe",productId:o.id,allowDiscountCodes:!0,requireBillingAddress:!1,successUrl:"http://infpik.store/success"});i=a.url}catch(e){console.error("Error creating checkout URL:",e)}const l=`http://infpik.store/products/${o.slug}`,d={name:o.name,description:o.description,images:o.images,price:o.price,currency:o.currency,isAvailable:o.isAvailable,id:o.id,url:l},n={items:[{name:"Home",url:"http://infpik.store"},{name:"Products",url:"http://infpik.store/products"},{name:o.name,url:l}]};return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:`${o.name} - InfPik`,description:o.description,image:o.images[0]||"/og-image.jpg",canonical:l,type:"product"},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"Product",data:d})} ${renderComponent(e,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:n})} ${maybeRenderHead()}<div class="container max-w-7xl"> <div class="mt-8 mb-8"> <nav class="flex items-center gap-2 text-sm text-primary-600"> <a href="/" class="hover:text-accent-600 transition-colors">Home</a> <span>/</span> <a href="/products" class="hover:text-accent-600 transition-colors">Products</a> <span>/</span> <span class="text-primary-900 font-medium">${o.name}</span> </nav> </div> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12"> <div class="lg:sticky lg:top-8 lg:self-start"> ${renderComponent(e,"ImageGallery",$$ImageGallery,{images:o.images,productName:o.name})} </div> <div class="space-y-8"> <div> <h1 class="text-3xl lg:text-4xl font-bold text-primary-900 mb-6">${o.name}</h1> </div> <div class="prose prose-gray max-w-none"> <h3 class="text-lg font-semibold text-primary-900 mb-3">Description</h3> <p class="text-primary-700 leading-relaxed">${o.description}</p> </div> ${o.tags&&o.tags.length>0&&renderTemplate`<div> <h3 class="text-lg font-semibold text-gray-900 mb-3">Similar</h3> <div class="flex flex-wrap gap-2"> ${o.tags.map((e=>renderTemplate`<a${addAttribute(`/products/tag/${e}`,"href")} class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 hover:text-gray-900 transition-colors duration-200 cursor-pointer"> ${e} </a>`))} </div> </div>`} <!-- Trust Signals --> ${renderComponent(e,"TrustSignals",$$TrustSignals,{class:"mb-6"})} <div class="pt-6 border-t border-primary-200"> <div class="flex gap-3"> ${i?renderTemplate`<a${addAttribute(i,"href")} class="flex-1 inline-flex items-center justify-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path> </svg> <span>Buy Now - ${formatPrice(o.price,o.currency)}</span> </a>`:renderTemplate`<button class="flex-1 bg-primary-300 text-primary-600 px-6 py-3 rounded-full font-semibold text-base cursor-not-allowed" disabled>
Checkout Unavailable
</button>`} <button class="inline-flex items-center justify-center gap-2 bg-primary-50 border-2 border-primary-200 text-primary-700 px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-primary-100 hover:border-primary-300 hover:text-primary-900" onclick="navigator.share ? navigator.share({title: document.title, url: window.location.href}) : navigator.clipboard.writeText(window.location.href)"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path> </svg> <span>Share</span> </button> </div> </div> <!-- Product Benefits --> ${renderComponent(e,"ProductBenefits",$$ProductBenefits,{class:"mt-8"})} </div> </div> <!-- Product FAQ --> ${renderComponent(e,"ProductFAQ",$$ProductFAQ,{class:"mt-12"})} <!-- Related Images Section --> ${renderComponent(e,"RelatedImages",$$RelatedImages,{currentProduct:o})} <div class="mt-16 text-center"> <h2 class="text-3xl font-bold text-primary-900 mb-4">You might also like</h2> <p class="text-primary-600 mb-8">Browse our full collection of digital images</p> <a href="/products" class="inline-flex items-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5">
View All Products
<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path> </svg> </a> </div> </div> `})}`}),"D:/code/image/polar-image-store/src/pages/products/[slug].astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/products/[slug].astro",$$url="/products/[slug]",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$slug,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};